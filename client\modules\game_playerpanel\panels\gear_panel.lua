-- gear_panel.lua
-- Handles the gear panel content and functionality

local GearPanel = {}

-- Equipment slot definitions with their positions and icons
local equipmentSlots = {
  head = { row = 1, col = 2, icon = '/images/game/slots/head.png', clientSlot = InventorySlotHead },
  neck = { row = 2, col = 2, icon = '/images/game/slots/neck.png', clientSlot = InventorySlotNecklace },
  back = { row = 2, col = 1, icon = '/images/game/slots/back.png', clientSlot = InventorySlotBackpack },
  body = { row = 3, col = 2, icon = '/images/game/slots/body.png', clientSlot = InventorySlotArmor },
  hand = { row = 3, col = 3, icon = '/images/game/slots/hand.png', clientSlot = InventorySlotHand },
  legs = { row = 4, col = 2, icon = '/images/game/slots/legs.png', clientSlot = InventorySlotLegs },
  feet = { row = 5, col = 2, icon = '/images/game/slots/feet.png', clientSlot = InventorySlotFeet },
  finger = { row = 2, col = 3, icon = '/images/game/slots/finger.png', clientSlot = InventorySlotRing },
  ammo = { row = 4, col = 3, icon = '/images/game/slots/ammo.png', clientSlot = InventorySlotAmmo }
}

-- Map server slot IDs to client slot names
local serverSlotToClientSlot = {
  [1] = 'head',    -- CONST_SLOT_HEAD
  [2] = 'neck',    -- CONST_SLOT_NECKLACE
  [3] = 'back',    -- CONST_SLOT_BACKPACK
  [4] = 'body',    -- CONST_SLOT_ARMOR
  [5] = 'hand',    -- CONST_SLOT_HAND
  [6] = 'legs',    -- CONST_SLOT_LEGS
  [7] = 'feet',    -- CONST_SLOT_FEET
  [8] = 'finger',  -- CONST_SLOT_RING
  [9] = 'ammo'     -- CONST_SLOT_THROWING
}

-- Store event connections for cleanup
local eventConnections = {}

function GearPanel.show(contentPanel)
  g_logger.info("GearPanel.show: Starting gear panel initialization")
  if not contentPanel then
    g_logger.error("GearPanel.show: contentPanel is nil")
    return
  end

  -- Add padding to the content panel
  contentPanel:setPaddingLeft(10)
  contentPanel:setPaddingRight(10)
  contentPanel:setPaddingTop(10)
  contentPanel:setPaddingBottom(10)

  -- Create a vertical layout for gear content
  local layout = UIVerticalLayout.create(contentPanel)
  layout:setSpacing(10)
  contentPanel:setLayout(layout)

  -- Create gear slots container
  local gearContainer = g_ui.createWidget('UIWidget', contentPanel)
  gearContainer:setHeight(220)
  gearContainer:setId('gearContainer')

  -- Create equipment slots grid (5 rows x 3 columns)
  local slotSize = 40
  local slotSpacing = 5

  -- Create equipment slots
  for slotName, slotData in pairs(equipmentSlots) do
    local slotWidget = g_ui.createWidget('EquipmentSlot', gearContainer)
    slotWidget:setId('slot_' .. slotName)

    -- Calculate position based on row and column using margins
    local marginLeft = 20 + (slotData.col - 1) * (slotSize + slotSpacing)
    local marginTop = 10 + (slotData.row - 1) * (slotSize + slotSpacing)

    slotWidget:addAnchor(AnchorLeft, 'parent', AnchorLeft)
    slotWidget:addAnchor(AnchorTop, 'parent', AnchorTop)
    slotWidget:setMarginLeft(marginLeft)
    slotWidget:setMarginTop(marginTop)

    -- Add slot icon (background icon)
    local iconWidget = g_ui.createWidget('EquipmentSlotIcon', slotWidget)
    iconWidget:setId('icon_' .. slotName)
    iconWidget:setImageSource(slotData.icon)

    -- Add item widget for equipped items
    local itemWidget = g_ui.createWidget('UIItem', slotWidget)
    itemWidget:setId('item_' .. slotName)
    itemWidget:setSize({32, 32})
    itemWidget:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    itemWidget:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
    itemWidget:setVisible(false) -- Initially hidden

    -- Store slot data for future use
    slotWidget.slotName = slotName
    slotWidget.slotData = slotData
    slotWidget.iconWidget = iconWidget
    slotWidget.itemWidget = itemWidget

    -- Set initial empty state appearance
    slotWidget:setBackgroundColor('#2a2a2a')
    slotWidget:setBorderColor('#555555')

    -- Add click handler for demo purposes
    slotWidget.onClick = function(widget)
      if widget.equippedItem then
        GearPanel.updateSlotItem(slotName, nil)
      else
        -- Create a demo item for testing
        local demoItem = Item.create(2160) -- Gold coin as demo
        GearPanel.updateSlotItem(slotName, demoItem)
      end
    end
  end

  -- Connect to inventory change events
  GearPanel.connectEvents()

  -- Initialize with current equipment (with a small delay to ensure player data is loaded)
  GearPanel.refreshAllSlots()

  -- Also schedule a delayed refresh in case the initial one was too early
  g_dispatcher.scheduleEvent(500, function()
    g_logger.info("GearPanel: Performing delayed refresh")
    GearPanel.refreshAllSlots()
  end) -- 500ms delay

  g_logger.info("GearPanel.show: Gear panel initialization completed")
end

function GearPanel.connectEvents()
  -- Disconnect any existing connections
  GearPanel.disconnectEvents()

  -- Connect to local player inventory changes
  local localPlayer = g_game.getLocalPlayer()
  if localPlayer then
    g_logger.info("GearPanel.connectEvents: Connecting to local player inventory events")
    eventConnections.inventoryChange = connect(localPlayer, {
      onInventoryChange = GearPanel.onInventoryChange
    })
  else
    g_logger.warning("GearPanel.connectEvents: No local player found, cannot connect events")
  end
end

function GearPanel.disconnectEvents()
  for _, connection in pairs(eventConnections) do
    if connection then
      disconnect(connection)
    end
  end
  eventConnections = {}
end

function GearPanel.onInventoryChange(inventory, item, oldItem)
  g_logger.info("GearPanel.onInventoryChange: inventory=" .. tostring(inventory) .. ", item=" .. (item and "present" or "nil") .. ", oldItem=" .. (oldItem and "present" or "nil"))

  -- Map server inventory slot to client slot name
  local slotName = serverSlotToClientSlot[inventory]
  if slotName then
    g_logger.info("GearPanel.onInventoryChange: Updating slot " .. slotName)
    GearPanel.updateSlotItem(slotName, item)
  else
    g_logger.warning("GearPanel.onInventoryChange: Unknown inventory slot " .. tostring(inventory))
  end
end

function GearPanel.refreshAllSlots()
  -- Update all slots with current equipment
  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.warning("GearPanel.refreshAllSlots: No local player found")
    return
  end

  g_logger.info("GearPanel.refreshAllSlots: Refreshing all equipment slots")

  for slotName, slotData in pairs(equipmentSlots) do
    local item = localPlayer:getInventoryItem(slotData.clientSlot)
    GearPanel.updateSlotItem(slotName, item)
  end
end

-- Helper functions for equipment management
function GearPanel.getSlotWidget(slotName)
  local contentPanel = g_ui.getRootWidget():recursiveGetChildById('contentPanel')
  if contentPanel then
    local gearContainer = contentPanel:recursiveGetChildById('gearContainer')
    if gearContainer then
      return gearContainer:recursiveGetChildById('slot_' .. slotName)
    end
  end
  return nil
end

function GearPanel.updateSlotItem(slotName, item)
  local slotWidget = GearPanel.getSlotWidget(slotName)
  if not slotWidget then
    g_logger.warning("GearPanel.updateSlotItem: Could not find slot widget for " .. slotName)
    return
  end

  local iconWidget = slotWidget.iconWidget
  local itemWidget = slotWidget.itemWidget

  if not iconWidget or not itemWidget then
    g_logger.warning("GearPanel.updateSlotItem: Missing icon or item widget for slot " .. slotName)
    return
  end

  if item then
    -- Item equipped
    g_logger.info("GearPanel.updateSlotItem: Setting item " .. item:getId() .. " for slot " .. slotName)
    slotWidget.equippedItem = item

    -- Show the item and hide the background icon
    itemWidget:setItem(item)
    itemWidget:setVisible(true)
    iconWidget:setVisible(false)

    -- Update visual appearance for equipped state
    slotWidget:setBackgroundColor('#3a3a3a')
    slotWidget:setBorderColor('#888888')

    g_logger.info("GearPanel.updateSlotItem: Item widget visible: " .. tostring(itemWidget:isVisible()) .. ", item set: " .. tostring(itemWidget:getItem() ~= nil))
  else
    -- Slot empty
    g_logger.info("GearPanel.updateSlotItem: Clearing slot " .. slotName)
    slotWidget.equippedItem = nil

    -- Hide the item and show the background icon
    itemWidget:setVisible(false)
    iconWidget:setVisible(true)

    -- Reset visual appearance for empty state
    slotWidget:setBackgroundColor('#2a2a2a')
    slotWidget:setBorderColor('#555555')
  end
end

function GearPanel.updateStats(attack, defense, armor, weight)
  local contentPanel = g_ui.getRootWidget():recursiveGetChildById('contentPanel')
  if contentPanel then
    local statsText = contentPanel:recursiveGetChildById('statsText')
    if statsText then
      local statsString = string.format('Attack: %d  Defense: %d  Armor: %d\nWeight: %.2f oz',
                                       attack or 0, defense or 0, armor or 0, weight or 0.00)
      statsText:setText(statsString)
    end
  end
end

function GearPanel.getEquippedItems()
  local equippedItems = {}
  for slotName, _ in pairs(equipmentSlots) do
    local slotWidget = GearPanel.getSlotWidget(slotName)
    if slotWidget and slotWidget.equippedItem then
      equippedItems[slotName] = slotWidget.equippedItem
    end
  end
  return equippedItems
end

function GearPanel.hide()
  -- Disconnect events when hiding
  GearPanel.disconnectEvents()
end

-- Debug function to manually check equipment
function GearPanel.debugEquipment()
  g_logger.info("=== GearPanel Debug Information ===")

  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.error("Debug: No local player found")
    return
  end

  g_logger.info("Debug: Local player found")

  for slotName, slotData in pairs(equipmentSlots) do
    local item = localPlayer:getInventoryItem(slotData.clientSlot)
    if item then
      g_logger.info("Debug: Slot " .. slotName .. " has item ID " .. item:getId() .. " (count: " .. item:getCount() .. ")")
    else
      g_logger.info("Debug: Slot " .. slotName .. " is empty")
    end
  end

  g_logger.info("=== End Debug Information ===")
end

-- Manual refresh function for testing
function GearPanel.manualRefresh()
  g_logger.info("GearPanel.manualRefresh: Manually refreshing gear panel")
  GearPanel.refreshAllSlots()

  -- Also debug the widget states
  g_logger.info("=== Widget State Debug ===")
  for slotName, slotData in pairs(equipmentSlots) do
    local slotWidget = GearPanel.getSlotWidget(slotName)
    if slotWidget then
      local itemWidget = slotWidget.itemWidget
      local iconWidget = slotWidget.iconWidget
      if itemWidget and iconWidget then
        g_logger.info("Slot " .. slotName .. ": itemWidget visible=" .. tostring(itemWidget:isVisible()) .. ", iconWidget visible=" .. tostring(iconWidget:isVisible()) .. ", hasItem=" .. tostring(itemWidget:getItem() ~= nil))
      end
    end
  end
  g_logger.info("=== End Widget State Debug ===")
end

return GearPanel